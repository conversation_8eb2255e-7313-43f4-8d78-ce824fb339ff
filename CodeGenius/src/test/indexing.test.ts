/**
 * 索引功能测试
 * Tests for indexing functionality
 */

import { describe, it, beforeEach, afterEach } from 'mocha'
import { expect } from 'chai'
import * as sinon from 'sinon'
import { IndexManager } from '../services/indexing/IndexManager'
import { ExtensionMessage } from '../shared/ExtensionMessage'

describe('IndexManager', () => {
  let indexManager: IndexManager
  let mockPostMessage: sinon.SinonStub
  let messages: ExtensionMessage[]

  beforeEach(() => {
    messages = []
    mockPostMessage = sinon.stub().callsFake(async (message: ExtensionMessage) => {
      messages.push(message)
    })
    indexManager = new IndexManager(mockPostMessage)
  })

  afterEach(() => {
    sinon.restore()
  })

  describe('startIndexing', () => {
    it('应该在暂停模式下发送暂停消息', async () => {
      const workspaceDirs = ['/test/workspace']
      
      await indexManager.startIndexing(workspaceDirs, true)
      
      expect(messages).to.have.length(1)
      expect(messages[0].type).to.equal('indexProgress')
      expect(messages[0].indexProgress?.status).to.equal('paused')
      expect(messages[0].indexProgress?.desc).to.equal('Initial Indexing Skipped')
    })

    it('应该在正常模式下开始索引', async () => {
      const workspaceDirs = ['/test/workspace']
      
      // 模拟索引过程
      const indexingPromise = indexManager.startIndexing(workspaceDirs, false)
      
      // 等待一小段时间让索引开始
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(messages.length).to.be.greaterThan(0)
      expect(messages[0].type).to.equal('indexProgress')
      expect(messages[0].indexProgress?.status).to.be.oneOf(['loading', 'indexing'])
    })

    it('应该处理空工作区目录', async () => {
      await indexManager.startIndexing([], false)
      
      expect(messages).to.have.length(1)
      expect(messages[0].indexProgress?.status).to.equal('done')
      expect(messages[0].indexProgress?.desc).to.equal('Nothing to index')
    })
  })

  describe('控制功能', () => {
    it('应该支持暂停索引', () => {
      expect(() => indexManager.pauseIndexing()).to.not.throw()
    })

    it('应该支持恢复索引', () => {
      expect(() => indexManager.resumeIndexing()).to.not.throw()
    })

    it('应该支持取消索引', () => {
      expect(() => indexManager.cancelIndexing()).to.not.throw()
    })

    it('应该正确报告索引状态', () => {
      expect(indexManager.isCurrentlyIndexing()).to.be.false
    })
  })

  describe('retrieveRelevantFiles', () => {
    it('应该返回空数组当检索失败时', async () => {
      const result = await indexManager.retrieveRelevantFiles('/test/workspace', 'test query')
      expect(result).to.be.an('array')
      expect(result).to.have.length(0)
    })
  })
})

describe('消息类型', () => {
  it('应该包含正确的索引进度消息类型', () => {
    const message: ExtensionMessage = {
      type: 'indexProgress',
      indexProgress: {
        progress: 0.5,
        desc: 'Indexing files...',
        status: 'indexing'
      }
    }
    
    expect(message.type).to.equal('indexProgress')
    expect(message.indexProgress?.progress).to.equal(0.5)
    expect(message.indexProgress?.status).to.equal('indexing')
  })

  it('应该支持所有索引状态', () => {
    const statuses = ['done', 'loading', 'indexing', 'paused', 'failed', 'disabled', 'cancelled']
    
    statuses.forEach(status => {
      const message: ExtensionMessage = {
        type: 'indexProgress',
        indexProgress: {
          progress: 0,
          status: status as any
        }
      }
      
      expect(message.indexProgress?.status).to.equal(status)
    })
  })
})

describe('配置集成', () => {
  it('应该正确处理配置选项', () => {
    // 这里可以添加配置相关的测试
    // 由于需要模拟 vscode.workspace.getConfiguration，暂时跳过
    expect(true).to.be.true
  })
})

// 集成测试示例
describe('集成测试', () => {
  it('应该完整地处理索引流程', async () => {
    const messages: ExtensionMessage[] = []
    const mockPostMessage = async (message: ExtensionMessage) => {
      messages.push(message)
    }
    
    const indexManager = new IndexManager(mockPostMessage)
    
    // 启动索引
    await indexManager.startIndexing(['/test'], false)
    
    // 验证消息序列
    expect(messages.length).to.be.greaterThan(0)
    
    // 第一个消息应该是开始索引
    const firstMessage = messages[0]
    expect(firstMessage.type).to.equal('indexProgress')
    expect(firstMessage.indexProgress?.status).to.be.oneOf(['loading', 'indexing', 'disabled'])
  })
})
