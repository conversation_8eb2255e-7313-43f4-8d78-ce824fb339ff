import i18n from "@/i18n";
import { getAsVar, VSC_TITLEBAR_INACTIVE_FOREGROUND } from "@/utils/vscStyles";
import { useCallback, useEffect, useState } from "react";
import { getProgressPercentage } from "../IndexingProgress/IndexingProgress";
import { useExtensionState } from "@/context/ExtensionStateContext";

export interface ChatIndexingProgressProps {
  isExpanded: boolean
}

function ChatIndexingProgress({
  isExpanded
}: ChatIndexingProgressProps) {
  const [progressTitle, setProgressTitle] = useState<string>('')
  const [progress, setProgress] = useState<number | undefined>(undefined)
  const [showProgress, setShowProgress] = useState<boolean>(false)
  const { codebaseIndex } = useExtensionState()
  const hideDelay = 3000

  useEffect(() => {
    if (typeof codebaseIndex?.progress === "number") {
      setProgress(Math.min(1, Math.max(0, codebaseIndex.progress)))
    } else {
      setProgress(undefined)
    }
  }, [codebaseIndex])

  useEffect(() => {
    if (typeof progress === "number") {
      if (progress >= 1) {
        setProgressTitle(`${i18n.get('chat.chatIndexingProgress.done')}`)
      } else {
        setProgressTitle(`${i18n.get('chat.chatIndexingProgress.indexing')}`)
      }
    }
  }, [progress])

  useEffect(() => {
    if (typeof progress === "number") {
      if (progress >= 0 && progress < 1) {
        setShowProgress(true)
      } else if (progress >= 1) {
        setTimeout(() => {
          setShowProgress(false)
        }, hideDelay)
      }
    } else {
      setShowProgress(false)
    }
  }, [progress, isExpanded])

  return (
    <>
      {typeof progress === "number" && showProgress && (<div style={{
        margin: "0 -15px",
        padding: "0 15px 8px",
        borderBottom: `0.5px solid color-mix(in srgb, ${getAsVar(VSC_TITLEBAR_INACTIVE_FOREGROUND)} 20%, transparent)`,
        background: "var(--panel-view-background)"
      }}>
        <div style={{
          border: `0.5px solid color-mix(in srgb, ${getAsVar(VSC_TITLEBAR_INACTIVE_FOREGROUND)} 20%, transparent)`,
          borderRadius: 3,
          padding: "0 12px",
          background: progress < 1 ? `color-mix(in srgb, var(--progress-background) 20%, transparent)` : `color-mix(in srgb, #53bd73 20%, transparent)`
        }}>
          {progress < 1 && (<p>{progressTitle}{getProgressPercentage(progress)}%</p>)}
          {progress >= 1 && (<p>{progressTitle}</p>)}
        </div>
      </div>)}
      {!showProgress && (<div style={{
        margin: "0 -15px",
        borderBottom: isExpanded ? `0.5px solid color-mix(in srgb, ${getAsVar(VSC_TITLEBAR_INACTIVE_FOREGROUND)} 20%, transparent)` : "none",
      }}></div>)}
    </>
  )
}

export default ChatIndexingProgress
