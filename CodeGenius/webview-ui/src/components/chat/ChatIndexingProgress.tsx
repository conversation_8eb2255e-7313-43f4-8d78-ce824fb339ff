import i18n from "@/i18n";
import { getAsVar, VSC_TITLEBAR_INACTIVE_FOREGROUND } from "@/utils/vscStyles";
import { useCallback, useEffect, useState } from "react";
import { getProgressPercentage } from "../IndexingProgress/IndexingProgress";
import { useExtensionState } from "@/context/ExtensionStateContext";

export interface ChatIndexingProgressProps {
  isExpanded: boolean
}

function ChatIndexingProgress({
  isExpanded
}: ChatIndexingProgressProps) {
  const [progressTitle, setProgressTitle] = useState<string>('')
  const [progress, setProgress] = useState<number | undefined>(undefined)
  const [showProgress, setShowProgress] = useState<boolean>(false)
  const [status, setStatus] = useState<string>('')
  const { indexProgress } = useExtensionState()
  const hideDelay = 3000

  useEffect(() => {
    if (indexProgress) {
      setProgress(Math.min(1, Math.max(0, indexProgress.progress)))
      setStatus(indexProgress.status || '')
    } else {
      setProgress(undefined)
      setStatus('')
    }
  }, [indexProgress])

  useEffect(() => {
    if (typeof progress === "number" && indexProgress) {
      if (status === "done") {
        setProgressTitle(indexProgress.desc || i18n.get('chat.chatIndexingProgress.done'))
      } else if (status === "failed") {
        setProgressTitle(indexProgress.desc || 'Indexing failed')
      } else if (status === "paused") {
        setProgressTitle(indexProgress.desc || 'Indexing paused')
      } else if (status === "cancelled") {
        setProgressTitle(indexProgress.desc || 'Indexing cancelled')
      } else if (status === "disabled") {
        setProgressTitle(indexProgress.desc || 'Indexing disabled')
      } else {
        setProgressTitle(indexProgress.desc || i18n.get('chat.chatIndexingProgress.indexing'))
      }
    }
  }, [progress, status, indexProgress])

  useEffect(() => {
    if (typeof progress === "number") {
      if (status === "indexing" || status === "loading") {
        setShowProgress(true)
      } else if (status === "done") {
        setTimeout(() => {
          setShowProgress(false)
        }, hideDelay)
      } else if (status === "failed" || status === "paused" || status === "cancelled") {
        setShowProgress(true)
      } else if (status === "disabled") {
        setShowProgress(false)
      }
    } else {
      setShowProgress(false)
    }
  }, [progress, status, isExpanded])

  const getBackgroundColor = () => {
    if (status === "done") {
      return `color-mix(in srgb, #53bd73 20%, transparent)`
    } else if (status === "failed") {
      return `color-mix(in srgb, #f14c4c 20%, transparent)`
    } else if (status === "paused") {
      return `color-mix(in srgb, #ffcc02 20%, transparent)`
    } else if (status === "cancelled") {
      return `color-mix(in srgb, #f14c4c 20%, transparent)`
    } else {
      return `color-mix(in srgb, var(--progress-background) 20%, transparent)`
    }
  }

  return (
    <>
      {typeof progress === "number" && showProgress && (<div style={{
        margin: "0 -15px",
        padding: "0 15px 8px",
        borderBottom: `0.5px solid color-mix(in srgb, ${getAsVar(VSC_TITLEBAR_INACTIVE_FOREGROUND)} 20%, transparent)`,
        background: "var(--panel-view-background)"
      }}>
        <div style={{
          border: `0.5px solid color-mix(in srgb, ${getAsVar(VSC_TITLEBAR_INACTIVE_FOREGROUND)} 20%, transparent)`,
          borderRadius: 3,
          padding: "0 12px",
          background: getBackgroundColor()
        }}>
          {(status === "indexing" || status === "loading") && progress < 1 && (
            <p>{progressTitle} {getProgressPercentage(progress)}%</p>
          )}
          {(status === "done" || status === "failed" || status === "paused" || status === "cancelled") && (
            <p>{progressTitle}</p>
          )}
        </div>
      </div>)}
      {!showProgress && (<div style={{
        margin: "0 -15px",
        borderBottom: isExpanded ? `0.5px solid color-mix(in srgb, ${getAsVar(VSC_TITLEBAR_INACTIVE_FOREGROUND)} 20%, transparent)` : "none",
      }}></div>)}
    </>
  )
}

export default ChatIndexingProgress
