import { useEffect, useRef, useState } from "react";
import IndexingProgressBar from "./IndexingProgressBar";
import IndexingProgressIndicator from "./IndexingProgressIndicator";
import IndexingProgressTitleText from "./IndexingProgressTitleText";
import IndexingProgressErrorText from "./IndexingProgressErrorText";
import { IndexingProgressUpdate } from "./type";
import IndexingProgressSubtext from "./IndexingProgressSubtext";

export interface IndexingProgressProps {
  progress: number
}

export function getProgressPercentage(
  progress: IndexingProgressUpdate["progress"],
) {
  return Math.min(100, Math.max(0, progress * 100));
}

function IndexingProgress({
  progress
}: IndexingProgressProps) {
  const [paused, setPaused] = useState<boolean | undefined>(undefined);
  const [update, setUpdate] = useState<IndexingProgressUpdate>({
    progress: 0.0,
    status: "loading",
  })

  // 不显示进度条下方文字链接
  let showSubText = false;
  // If sidebar is opened after extension initializes, retrieve saved states.
  let initialized = false;

  useEffect(() => {
    if (!initialized) {
      // Triggers retrieval for possible non-default states set prior to IndexingProgressBar initialization
      initialized = true;
    }
  }, []);

  useEffect(() => {
    if (progress <= 0) {
      setUpdate({
        progress: 0.0,
        status: "loading",
      })
    } else if (progress >= 1) {
      setUpdate({
        progress: 1.0,
        status: "done",
      })
    } else {
      setUpdate({
        progress,
        status: "indexing",
      })
    }
  }, [progress])

  function onClick() {
    switch (update.status) {
      case "failed":
      break
      case "indexing":
      case "loading":
      case "paused":
        if (update.progress < 1 && update.progress >= 0) {
          setPaused((prev) => !prev)
        }
      break
      case "disabled":
      break
      case "done":
      default:
      break
    }
  }

  return (
    <div className="mt-4 flex flex-col">
      <div className="mb-0 flex justify-between text-sm">
        <IndexingProgressTitleText update={update} />
        {update.status !== "loading" && (
          <IndexingProgressIndicator update={update} />
        )}
      </div>

      <IndexingProgressBar update={update} />

      {showSubText && (<IndexingProgressSubtext update={update} onClick={onClick} />)}

      {update.status === "failed" && (
        <div className="mt-4">
          <IndexingProgressErrorText update={update} />
        </div>
      )}
    </div>
  );
}

export default IndexingProgress
