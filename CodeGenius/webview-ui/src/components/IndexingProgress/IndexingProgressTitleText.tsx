import i18n from "@/i18n";
import { IndexingProgressUpdate } from "./type"

export interface IndexingProgressTitleTextProps {
  update: IndexingProgressUpdate;
}

const STATUS_TO_TEXT: Record<IndexingProgressUpdate["status"], string> = {
  done: i18n.get('setting.indexingSettingsSection.indexingProgressTitle.done'),
  loading: i18n.get('setting.indexingSettingsSection.indexingProgressTitle.loading'),
  indexing: i18n.get('setting.indexingSettingsSection.indexingProgressTitle.indexing'),
  paused: "Indexing paused",
  failed: "Indexing failed",
  disabled: "Indexing disabled",
  cancelled: "Indexing cancelled",
}

function IndexingProgressTitleText({ update }: IndexingProgressTitleTextProps) {
  return (
    <span>
      {STATUS_TO_TEXT[update.status]}
    </span>
  )
}

export default IndexingProgressTitleText
