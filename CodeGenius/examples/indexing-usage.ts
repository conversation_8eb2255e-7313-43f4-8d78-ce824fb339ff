/**
 * 代码库索引功能使用示例
 * Example usage of codebase indexing functionality
 */

import { IndexManager } from '../src/services/indexing/IndexManager'
import { ExtensionMessage, IndexingProgressUpdate } from '../src/shared/ExtensionMessage'

// 模拟消息发送函数
const mockPostMessage = async (message: ExtensionMessage): Promise<void> => {
  console.log('发送消息到前端:', message)

  if (message.type === 'indexProgress' && message.indexProgress) {
    const { progress, desc, status } = message.indexProgress
    console.log(`索引进度: ${(progress * 100).toFixed(1)}% - ${desc} (${status})`)
  }
}

// 使用示例
async function exampleUsage() {
  // 创建索引管理器
  const indexManager = new IndexManager(mockPostMessage)

  // 模拟工作区目录
  const workspaceDirs = [
    '/path/to/your/project',
    '/path/to/another/project'
  ]

  console.log('开始索引...')

  try {
    // 启动索引（不暂停）
    await indexManager.startIndexing(workspaceDirs, false)

    console.log('索引完成！')

    // 检索相关文件
    const relevantFiles = await indexManager.retrieveRelevantFiles(
      workspaceDirs[0],
      'authentication login user'
    )

    console.log('相关文件:', relevantFiles)

  } catch (error) {
    console.error('索引过程中出现错误:', error)
  }
}

// 控制示例
async function controlExample() {
  const indexManager = new IndexManager(mockPostMessage)
  const workspaceDirs = ['/path/to/project']

  // 启动索引
  const indexingPromise = indexManager.startIndexing(workspaceDirs, false)

  // 模拟用户操作
  setTimeout(() => {
    console.log('暂停索引...')
    indexManager.pauseIndexing()
  }, 2000)

  setTimeout(() => {
    console.log('恢复索引...')
    indexManager.resumeIndexing()
  }, 5000)

  setTimeout(() => {
    console.log('取消索引...')
    indexManager.cancelIndexing()
  }, 8000)

  await indexingPromise
}

// 配置示例
function configurationExample() {
  console.log(`
配置索引功能：

1. 在 VSCode 设置中配置：
   - employeeZero.pauseCodebaseIndexOnStart: 启动时暂停索引
   - employeeZero.disableIndexing: 完全禁用索引

2. 或在 settings.json 中添加：
{
  "employeeZero.pauseCodebaseIndexOnStart": false,
  "employeeZero.disableIndexing": false
}

3. 前端控制：
   - 发送 "pauseIndexing" 消息暂停索引
   - 发送 "resumeIndexing" 消息恢复索引
   - 发送 "cancelIndexing" 消息取消索引
`)
}

// 运行示例
if (require.main === module) {
  console.log('=== 代码库索引功能示例 ===\n')

  configurationExample()

  console.log('\n=== 基本使用示例 ===')
  exampleUsage().catch(console.error)

  console.log('\n=== 控制功能示例 ===')
  controlExample().catch(console.error)
}

export {
  exampleUsage,
  controlExample,
  configurationExample
}
